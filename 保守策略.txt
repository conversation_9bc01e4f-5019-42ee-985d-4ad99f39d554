//@version=5
strategy("保守策略", overlay=true, initial_capital=1000, default_qty_type=strategy.percent_of_equity, default_qty_value=1, pyramiding=999, commission_type=strategy.commission.percent, commission_value=0.075)

// ==================== 策略参数设置 ====================
// 基础设置说明
// 金字塔功能：999层（允许多次加仓）
// 手续费设置：0.075%（双向手续费）

// 网格参数
grid_levels = input.int(25, "网格层数", minval=3, maxval=50)

// 25层独立网格间距设置
grid_spacing_1 = input.float(1.2, "第1层间距(%)", minval=0.1, maxval=50.0, step=0.1, group="网格间距设置")
grid_spacing_2 = input.float(1.2, "第2层间距(%)", minval=0.1, maxval=50.0, step=0.1, group="网格间距设置")
grid_spacing_3 = input.float(1.2, "第3层间距(%)", minval=0.1, maxval=50.0, step=0.1, group="网格间距设置")
grid_spacing_4 = input.float(1.2, "第4层间距(%)", minval=0.1, maxval=50.0, step=0.1, group="网格间距设置")
grid_spacing_5 = input.float(2.4, "第5层间距(%)", minval=0.1, maxval=50.0, step=0.1, group="网格间距设置")
grid_spacing_6 = input.float(2.4, "第6层间距(%)", minval=0.1, maxval=50.0, step=0.1, group="网格间距设置")
grid_spacing_7 = input.float(2.4, "第7层间距(%)", minval=0.1, maxval=50.0, step=0.1, group="网格间距设置")
grid_spacing_8 = input.float(2.4, "第8层间距(%)", minval=0.1, maxval=50.0, step=0.1, group="网格间距设置")
grid_spacing_9 = input.float(3.3, "第9层间距(%)", minval=0.1, maxval=50.0, step=0.1, group="网格间距设置")
grid_spacing_10 = input.float(3.3, "第10层间距(%)", minval=0.1, maxval=50.0, step=0.1, group="网格间距设置")
grid_spacing_11 = input.float(3.3, "第11层间距(%)", minval=0.1, maxval=50.0, step=0.1, group="网格间距设置")
grid_spacing_12 = input.float(3.3, "第12层间距(%)", minval=0.1, maxval=50.0, step=0.1, group="网格间距设置")
grid_spacing_13 = input.float(3.3, "第13层间距(%)", minval=0.1, maxval=50.0, step=0.1, group="网格间距设置")
grid_spacing_14 = input.float(3.3, "第14层间距(%)", minval=0.1, maxval=50.0, step=0.1, group="网格间距设置")
grid_spacing_15 = input.float(3.3, "第15层间距(%)", minval=0.1, maxval=50.0, step=0.1, group="网格间距设置")
grid_spacing_16 = input.float(3.3, "第16层间距(%)", minval=0.1, maxval=50.0, step=0.1, group="网格间距设置")
grid_spacing_17 = input.float(3.3, "第17层间距(%)", minval=0.1, maxval=50.0, step=0.1, group="网格间距设置")
grid_spacing_18 = input.float(3.3, "第18层间距(%)", minval=0.1, maxval=50.0, step=0.1, group="网格间距设置")
grid_spacing_19 = input.float(3.3, "第19层间距(%)", minval=0.1, maxval=50.0, step=0.1, group="网格间距设置")
grid_spacing_20 = input.float(3.3, "第20层间距(%)", minval=0.1, maxval=50.0, step=0.1, group="网格间距设置")
grid_spacing_21 = input.float(3.3, "第21层间距(%)", minval=0.1, maxval=50.0, step=0.1, group="网格间距设置")
grid_spacing_22 = input.float(3.3, "第22层间距(%)", minval=0.1, maxval=50.0, step=0.1, group="网格间距设置")
grid_spacing_23 = input.float(3.3, "第23层间距(%)", minval=0.1, maxval=50.0, step=0.1, group="网格间距设置")
grid_spacing_24 = input.float(3.3, "第24层间距(%)", minval=0.1, maxval=50.0, step=0.1, group="网格间距设置")
grid_spacing_25 = input.float(3.3, "第25层间距(%)", minval=0.1, maxval=50.0, step=0.1, group="网格间距设置")

// 单网格追踪止盈参数
grid_trailing_1_target = input.float(1.0, "追踪止盈1目标(%)", minval=0.1, maxval=20.0, step=0.1, group="单网格追踪止盈")
grid_trailing_1_callback = input.float(0.2, "追踪止盈1回调(%)", minval=0.1, maxval=5.0, step=0.1, group="单网格追踪止盈")
grid_trailing_2_target = input.float(2.0, "追踪止盈2目标(%)", minval=0.1, maxval=20.0, step=0.1, group="单网格追踪止盈")
grid_trailing_2_callback = input.float(0.3, "追踪止盈2回调(%)", minval=0.1, maxval=5.0, step=0.1, group="单网格追踪止盈")
grid_trailing_3_target = input.float(3.0, "追踪止盈3目标(%)", minval=0.1, maxval=20.0, step=0.1, group="单网格追踪止盈")
grid_trailing_3_callback = input.float(0.4, "追踪止盈3回调(%)", minval=0.1, maxval=5.0, step=0.1, group="单网格追踪止盈")
grid_trailing_4_target = input.float(4.0, "追踪止盈4目标(%)", minval=0.1, maxval=20.0, step=0.1, group="单网格追踪止盈")
grid_trailing_4_callback = input.float(0.5, "追踪止盈4回调(%)", minval=0.1, maxval=5.0, step=0.1, group="单网格追踪止盈")

// 回调确认参数
rebound_confirmation_enabled = input.bool(true, "启用回调确认建仓", group="回调确认设置")

// 25层独立回调确认幅度设置
rebound_pct_1 = input.float(0.2, "第1层回调幅度(%)", minval=0.1, maxval=5.0, step=0.1, group="回调确认幅度设置")
rebound_pct_2 = input.float(0.2, "第2层回调幅度(%)", minval=0.1, maxval=5.0, step=0.1, group="回调确认幅度设置")
rebound_pct_3 = input.float(0.2, "第3层回调幅度(%)", minval=0.1, maxval=5.0, step=0.1, group="回调确认幅度设置")
rebound_pct_4 = input.float(0.2, "第4层回调幅度(%)", minval=0.1, maxval=5.0, step=0.1, group="回调确认幅度设置")
rebound_pct_5 = input.float(0.3, "第5层回调幅度(%)", minval=0.1, maxval=5.0, step=0.1, group="回调确认幅度设置")
rebound_pct_6 = input.float(0.3, "第6层回调幅度(%)", minval=0.1, maxval=5.0, step=0.1, group="回调确认幅度设置")
rebound_pct_7 = input.float(0.3, "第7层回调幅度(%)", minval=0.1, maxval=5.0, step=0.1, group="回调确认幅度设置")
rebound_pct_8 = input.float(0.3, "第8层回调幅度(%)", minval=0.1, maxval=5.0, step=0.1, group="回调确认幅度设置")
rebound_pct_9 = input.float(0.4, "第9层回调幅度(%)", minval=0.1, maxval=5.0, step=0.1, group="回调确认幅度设置")
rebound_pct_10 = input.float(0.4, "第10层回调幅度(%)", minval=0.1, maxval=5.0, step=0.1, group="回调确认幅度设置")
rebound_pct_11 = input.float(0.4, "第11层回调幅度(%)", minval=0.1, maxval=5.0, step=0.1, group="回调确认幅度设置")
rebound_pct_12 = input.float(0.4, "第12层回调幅度(%)", minval=0.1, maxval=5.0, step=0.1, group="回调确认幅度设置")
rebound_pct_13 = input.float(0.5, "第13层回调幅度(%)", minval=0.1, maxval=5.0, step=0.1, group="回调确认幅度设置")
rebound_pct_14 = input.float(0.5, "第14层回调幅度(%)", minval=0.1, maxval=5.0, step=0.1, group="回调确认幅度设置")
rebound_pct_15 = input.float(0.5, "第15层回调幅度(%)", minval=0.1, maxval=5.0, step=0.1, group="回调确认幅度设置")
rebound_pct_16 = input.float(0.5, "第16层回调幅度(%)", minval=0.1, maxval=5.0, step=0.1, group="回调确认幅度设置")
rebound_pct_17 = input.float(0.6, "第17层回调幅度(%)", minval=0.1, maxval=5.0, step=0.1, group="回调确认幅度设置")
rebound_pct_18 = input.float(0.6, "第18层回调幅度(%)", minval=0.1, maxval=5.0, step=0.1, group="回调确认幅度设置")
rebound_pct_19 = input.float(0.6, "第19层回调幅度(%)", minval=0.1, maxval=5.0, step=0.1, group="回调确认幅度设置")
rebound_pct_20 = input.float(0.6, "第20层回调幅度(%)", minval=0.1, maxval=5.0, step=0.1, group="回调确认幅度设置")
rebound_pct_21 = input.float(0.7, "第21层回调幅度(%)", minval=0.1, maxval=5.0, step=0.1, group="回调确认幅度设置")
rebound_pct_22 = input.float(0.7, "第22层回调幅度(%)", minval=0.1, maxval=5.0, step=0.1, group="回调确认幅度设置")
rebound_pct_23 = input.float(0.7, "第23层回调幅度(%)", minval=0.1, maxval=5.0, step=0.1, group="回调确认幅度设置")
rebound_pct_24 = input.float(0.7, "第24层回调幅度(%)", minval=0.1, maxval=5.0, step=0.1, group="回调确认幅度设置")
rebound_pct_25 = input.float(0.8, "第25层回调幅度(%)", minval=0.1, maxval=5.0, step=0.1, group="回调确认幅度设置")

// 马丁参数
martingale_enabled = input.bool(true, "启用马丁策略")
martingale_multiplier = input.float(2.0, "马丁倍数", minval=1, maxval=3.0, step=0.1)

// 止盈止损参数
overall_profit_target = input.float(3.0, "整体止盈(%)", minval=0.1, maxval=20.0, step=0.5)
trailing_stop_enabled = input.bool(true, "启用追踪止盈")
trailing_stop_pct = input.float(0.5, "追踪止盈回撤(%)", minval=0.1, maxval=8.0, step=0.5)

// 回测时间设置
start_date = input.time(timestamp("2025-06-01 00:00"), "回测开始时间")
end_date = input.time(timestamp("2025-12-31 23:59"), "回测结束时间")

// 图表显示参数
show_grid_lines = input.bool(true, "显示网格线")
grid_display_count = input.int(4, "显示网格数量", minval=1, maxval=25)

// ==================== 全局变量 ====================
var array<float> grid_prices = array.new<float>()
var array<bool> grid_filled = array.new<bool>()
var array<string> grid_ids = array.new<string>()
var array<float> grid_entry_prices = array.new<float>()
var array<float> grid_highest_profits = array.new<float>()

// 回调确认相关数组
var array<bool> grid_triggered = array.new<bool>()      // 记录网格是否已触发（价格跌破网格线）
var array<float> grid_min_prices = array.new<float>()   // 记录触发后的最低价
var array<bool> grid_waiting_rebound = array.new<bool>() // 记录是否正在等待回调确认

var float base_price = na
var float total_position_size = 0.0
var float avg_entry_price = 0.0
var int martingale_count = 0
var float highest_profit = 0.0
var bool strategy_active = false
var bool entry_executed_this_bar = false  // 控制每个K线只能建仓一次
var float last_entry_price = 0.0  // 记录上次建仓价格
var bool waiting_for_decline = false  // 是否正在等待下跌

// ==================== 辅助函数 ====================
// 获取指定层级的网格间距
get_grid_spacing(layer) =>
    switch layer
        1 => grid_spacing_1
        2 => grid_spacing_2
        3 => grid_spacing_3
        4 => grid_spacing_4
        5 => grid_spacing_5
        6 => grid_spacing_6
        7 => grid_spacing_7
        8 => grid_spacing_8
        9 => grid_spacing_9
        10 => grid_spacing_10
        11 => grid_spacing_11
        12 => grid_spacing_12
        13 => grid_spacing_13
        14 => grid_spacing_14
        15 => grid_spacing_15
        16 => grid_spacing_16
        17 => grid_spacing_17
        18 => grid_spacing_18
        19 => grid_spacing_19
        20 => grid_spacing_20
        21 => grid_spacing_21
        22 => grid_spacing_22
        23 => grid_spacing_23
        24 => grid_spacing_24
        25 => grid_spacing_25
        => 2.0  // 默认值

// 获取指定层级的回调确认幅度
get_rebound_pct(layer) =>
    switch layer
        1 => rebound_pct_1
        2 => rebound_pct_2
        3 => rebound_pct_3
        4 => rebound_pct_4
        5 => rebound_pct_5
        6 => rebound_pct_6
        7 => rebound_pct_7
        8 => rebound_pct_8
        9 => rebound_pct_9
        10 => rebound_pct_10
        11 => rebound_pct_11
        12 => rebound_pct_12
        13 => rebound_pct_13
        14 => rebound_pct_14
        15 => rebound_pct_15
        16 => rebound_pct_16
        17 => rebound_pct_17
        18 => rebound_pct_18
        19 => rebound_pct_19
        20 => rebound_pct_20
        21 => rebound_pct_21
        22 => rebound_pct_22
        23 => rebound_pct_23
        24 => rebound_pct_24
        25 => rebound_pct_25
        => 0.3  // 默认值

// ==================== 主策略逻辑 ====================
// 时间过滤
in_date_range = time >= start_date and time <= end_date

// 初始化网格（只在回测时间范围内且第一次运行时）
if in_date_range and na(base_price)
    base_price := close
    strategy_active := true

    // 清空并初始化数组
    array.clear(grid_prices)
    array.clear(grid_filled)
    array.clear(grid_ids)
    array.clear(grid_entry_prices)
    array.clear(grid_highest_profits)
    array.clear(grid_triggered)
    array.clear(grid_min_prices)
    array.clear(grid_waiting_rebound)

    // 首单均价建仓
    first_position_size = strategy.initial_capital * 0.1 / close
    strategy.entry("首单", strategy.long, qty=first_position_size)
    total_position_size := first_position_size
    avg_entry_price := close
    // 首单建仓后也需要等待下跌
    last_entry_price := close
    waiting_for_decline := true

    // 创建25个网格层（使用累积间距计算）
    cumulative_spacing = 0.0
    for i = 1 to grid_levels
        layer_spacing = get_grid_spacing(i)
        cumulative_spacing := cumulative_spacing + layer_spacing
        grid_price = base_price * (1 - cumulative_spacing / 100)
        array.push(grid_prices, grid_price)
        array.push(grid_filled, false)
        array.push(grid_ids, "建仓-" + str.tostring(i))
        array.push(grid_entry_prices, 0.0)
        array.push(grid_highest_profits, 0.0)
        array.push(grid_triggered, false)
        array.push(grid_min_prices, 0.0)
        array.push(grid_waiting_rebound, false)

// 每个K线开始时重置建仓控制变量
entry_executed_this_bar := false

// 检查是否需要等待下跌
if waiting_for_decline and last_entry_price > 0
    // 如果当前价格低于上次建仓价格，说明出现了下跌，可以重新开始建仓流程
    if close < last_entry_price
        waiting_for_decline := false

// 检查网格触发（只在回测时间范围内）
if in_date_range and strategy_active and array.size(grid_prices) > 0 and not entry_executed_this_bar and not waiting_for_decline
    for i = 0 to array.size(grid_prices) - 1
        grid_price = array.get(grid_prices, i)
        is_filled = array.get(grid_filled, i)
        grid_id = array.get(grid_ids, i)
        is_triggered = array.get(grid_triggered, i)
        min_price = array.get(grid_min_prices, i)
        waiting_rebound = array.get(grid_waiting_rebound, i)

        // 如果网格未建仓
        if not is_filled
            if rebound_confirmation_enabled
                // 回调确认模式
                if close <= grid_price and not is_triggered
                    // 首次触发网格线，开始追踪最低价
                    array.set(grid_triggered, i, true)
                    array.set(grid_min_prices, i, close)
                    array.set(grid_waiting_rebound, i, true)

                if is_triggered and waiting_rebound
                    // 更新最低价
                    if close < min_price
                        array.set(grid_min_prices, i, close)

                    // 检查回调确认条件
                    rebound_pct = (close - min_price) / min_price * 100
                    required_rebound_pct = get_rebound_pct(i + 1)  // 转换为1-based索引
                    if rebound_pct >= required_rebound_pct
                        // 满足回调确认条件，执行建仓
                        position_size = strategy.initial_capital * 0.1 / close

                        // 应用马丁倍数（从第二层开始）
                        if martingale_enabled and i > 0
                            position_size := position_size * martingale_multiplier

                        strategy.entry(grid_id, strategy.long, qty=position_size)
                        array.set(grid_filled, i, true)
                        array.set(grid_entry_prices, i, close)
                        array.set(grid_highest_profits, i, 0.0)
                        // 重置回调确认状态
                        array.set(grid_triggered, i, false)
                        array.set(grid_min_prices, i, 0.0)
                        array.set(grid_waiting_rebound, i, false)

                        // 更新平均成本计算（考虑首单建仓）
                        old_total_cost = avg_entry_price * total_position_size
                        new_cost = close * position_size
                        total_position_size := total_position_size + position_size
                        avg_entry_price := (old_total_cost + new_cost) / total_position_size

                        // 标记本K线已执行建仓，设置等待下跌状态，跳出循环
                        entry_executed_this_bar := true
                        last_entry_price := close
                        waiting_for_decline := true
                        break
            else
                // 原始模式：价格触及网格线即建仓
                if close <= grid_price
                    position_size = strategy.initial_capital * 0.1 / close

                    // 应用马丁倍数（从第二层开始）
                    if martingale_enabled and i > 0
                        position_size := position_size * martingale_multiplier

                    strategy.entry(grid_id, strategy.long, qty=position_size)
                    array.set(grid_filled, i, true)
                    array.set(grid_entry_prices, i, close)
                    array.set(grid_highest_profits, i, 0.0)
                    // 更新平均成本计算（考虑首单建仓）
                    old_total_cost = avg_entry_price * total_position_size
                    new_cost = close * position_size
                    total_position_size := total_position_size + position_size
                    avg_entry_price := (old_total_cost + new_cost) / total_position_size

                    // 标记本K线已执行建仓，设置等待下跌状态，跳出循环
                    entry_executed_this_bar := true
                    last_entry_price := close
                    waiting_for_decline := true
                    break

// 检查止盈条件（只在回测时间范围内）
if in_date_range and strategy.position_size > 0 and array.size(grid_prices) > 0
    current_profit_pct = (close - avg_entry_price) / avg_entry_price * 100

    // 更新最高盈利
    if current_profit_pct > highest_profit
        highest_profit := current_profit_pct

    // 单网格追踪止盈检查
    for i = 0 to array.size(grid_prices) - 1
        grid_price = array.get(grid_prices, i)
        is_filled = array.get(grid_filled, i)
        grid_id = array.get(grid_ids, i)

        if is_filled
            entry_price = array.get(grid_entry_prices, i)
            current_profit_pct = (close - entry_price) / entry_price * 100
            highest_profit_pct = array.get(grid_highest_profits, i)

            // 更新最高盈利
            if current_profit_pct > highest_profit_pct
                array.set(grid_highest_profits, i, current_profit_pct)
                highest_profit_pct := current_profit_pct

            // 检查追踪止盈条件
            should_close = false
            close_reason = ""

            // 追踪止盈1: 1%目标，0.2%回调
            if highest_profit_pct >= grid_trailing_1_target and current_profit_pct <= highest_profit_pct - grid_trailing_1_callback
                should_close := true
                close_reason := "追踪止盈1"

            // 追踪止盈2: 2%目标，0.3%回调
            if highest_profit_pct >= grid_trailing_2_target and current_profit_pct <= highest_profit_pct - grid_trailing_2_callback
                should_close := true
                close_reason := "追踪止盈2"

            // 追踪止盈3: 3%目标，0.4%回调
            if highest_profit_pct >= grid_trailing_3_target and current_profit_pct <= highest_profit_pct - grid_trailing_3_callback
                should_close := true
                close_reason := "追踪止盈3"

            // 追踪止盈4: 4%目标，0.5%回调
            if highest_profit_pct >= grid_trailing_4_target and current_profit_pct <= highest_profit_pct - grid_trailing_4_callback
                should_close := true
                close_reason := "追踪止盈4"

            // 执行平仓
            if should_close
                strategy.close(grid_id, comment=close_reason)
                array.set(grid_filled, i, false)
                array.set(grid_entry_prices, i, 0.0)
                array.set(grid_highest_profits, i, 0.0)
                // 重置回调确认状态
                array.set(grid_triggered, i, false)
                array.set(grid_min_prices, i, 0.0)
                array.set(grid_waiting_rebound, i, false)

    // 整体止盈检查
    if current_profit_pct >= overall_profit_target
        if trailing_stop_enabled
            // 追踪止盈
            if current_profit_pct < highest_profit - trailing_stop_pct
                strategy.close_all("追踪止盈")
                // 重置策略状态
                base_price := na
                total_position_size := 0.0
                avg_entry_price := 0.0
                martingale_count := 0
                highest_profit := 0.0
                strategy_active := false
                last_entry_price := 0.0
                waiting_for_decline := false
                array.clear(grid_filled)
                array.clear(grid_entry_prices)
                array.clear(grid_highest_profits)
                array.clear(grid_triggered)
                array.clear(grid_min_prices)
                array.clear(grid_waiting_rebound)
        else
            strategy.close_all("整体止盈")
            // 重置策略状态
            base_price := na
            total_position_size := 0.0
            avg_entry_price := 0.0
            martingale_count := 0
            highest_profit := 0.0
            strategy_active := false
            last_entry_price := 0.0
            waiting_for_decline := false
            array.clear(grid_filled)
            array.clear(grid_entry_prices)
            array.clear(grid_highest_profits)
            array.clear(grid_triggered)
            array.clear(grid_min_prices)
            array.clear(grid_waiting_rebound)

// ==================== 图表显示 ====================
// 绘制基准价格线
plot(base_price, "基准价格", color=color.yellow, linewidth=2)

// 准备网格线数据（在全局作用域中）
display_count = show_grid_lines and array.size(grid_prices) > 0 ? math.min(grid_display_count, array.size(grid_prices)) : 0

// 绘制所有25个网格线
grid_1 = display_count > 0 and array.size(grid_prices) > 0 ? array.get(grid_prices, 0) : na
grid_2 = display_count > 1 and array.size(grid_prices) > 1 ? array.get(grid_prices, 1) : na
grid_3 = display_count > 2 and array.size(grid_prices) > 2 ? array.get(grid_prices, 2) : na
grid_4 = display_count > 3 and array.size(grid_prices) > 3 ? array.get(grid_prices, 3) : na
grid_5 = display_count > 4 and array.size(grid_prices) > 4 ? array.get(grid_prices, 4) : na
grid_6 = display_count > 5 and array.size(grid_prices) > 5 ? array.get(grid_prices, 5) : na
grid_7 = display_count > 6 and array.size(grid_prices) > 6 ? array.get(grid_prices, 6) : na
grid_8 = display_count > 7 and array.size(grid_prices) > 7 ? array.get(grid_prices, 7) : na
grid_9 = display_count > 8 and array.size(grid_prices) > 8 ? array.get(grid_prices, 8) : na
grid_10 = display_count > 9 and array.size(grid_prices) > 9 ? array.get(grid_prices, 9) : na
grid_11 = display_count > 10 and array.size(grid_prices) > 10 ? array.get(grid_prices, 10) : na
grid_12 = display_count > 11 and array.size(grid_prices) > 11 ? array.get(grid_prices, 11) : na
grid_13 = display_count > 12 and array.size(grid_prices) > 12 ? array.get(grid_prices, 12) : na
grid_14 = display_count > 13 and array.size(grid_prices) > 13 ? array.get(grid_prices, 13) : na
grid_15 = display_count > 14 and array.size(grid_prices) > 14 ? array.get(grid_prices, 14) : na
grid_16 = display_count > 15 and array.size(grid_prices) > 15 ? array.get(grid_prices, 15) : na
grid_17 = display_count > 16 and array.size(grid_prices) > 16 ? array.get(grid_prices, 16) : na
grid_18 = display_count > 17 and array.size(grid_prices) > 17 ? array.get(grid_prices, 17) : na
grid_19 = display_count > 18 and array.size(grid_prices) > 18 ? array.get(grid_prices, 18) : na
grid_20 = display_count > 19 and array.size(grid_prices) > 19 ? array.get(grid_prices, 19) : na
grid_21 = display_count > 20 and array.size(grid_prices) > 20 ? array.get(grid_prices, 20) : na
grid_22 = display_count > 21 and array.size(grid_prices) > 21 ? array.get(grid_prices, 21) : na
grid_23 = display_count > 22 and array.size(grid_prices) > 22 ? array.get(grid_prices, 22) : na
grid_24 = display_count > 23 and array.size(grid_prices) > 23 ? array.get(grid_prices, 23) : na
grid_25 = display_count > 24 and array.size(grid_prices) > 24 ? array.get(grid_prices, 24) : na

filled_1 = display_count > 0 and array.size(grid_filled) > 0 ? array.get(grid_filled, 0) : false
filled_2 = display_count > 1 and array.size(grid_filled) > 1 ? array.get(grid_filled, 1) : false
filled_3 = display_count > 2 and array.size(grid_filled) > 2 ? array.get(grid_filled, 2) : false
filled_4 = display_count > 3 and array.size(grid_filled) > 3 ? array.get(grid_filled, 3) : false
filled_5 = display_count > 4 and array.size(grid_filled) > 4 ? array.get(grid_filled, 4) : false
filled_6 = display_count > 5 and array.size(grid_filled) > 5 ? array.get(grid_filled, 5) : false
filled_7 = display_count > 6 and array.size(grid_filled) > 6 ? array.get(grid_filled, 6) : false
filled_8 = display_count > 7 and array.size(grid_filled) > 7 ? array.get(grid_filled, 7) : false
filled_9 = display_count > 8 and array.size(grid_filled) > 8 ? array.get(grid_filled, 8) : false
filled_10 = display_count > 9 and array.size(grid_filled) > 9 ? array.get(grid_filled, 9) : false
filled_11 = display_count > 10 and array.size(grid_filled) > 10 ? array.get(grid_filled, 10) : false
filled_12 = display_count > 11 and array.size(grid_filled) > 11 ? array.get(grid_filled, 11) : false
filled_13 = display_count > 12 and array.size(grid_filled) > 12 ? array.get(grid_filled, 12) : false
filled_14 = display_count > 13 and array.size(grid_filled) > 13 ? array.get(grid_filled, 13) : false
filled_15 = display_count > 14 and array.size(grid_filled) > 14 ? array.get(grid_filled, 14) : false
filled_16 = display_count > 15 and array.size(grid_filled) > 15 ? array.get(grid_filled, 15) : false
filled_17 = display_count > 16 and array.size(grid_filled) > 16 ? array.get(grid_filled, 16) : false
filled_18 = display_count > 17 and array.size(grid_filled) > 17 ? array.get(grid_filled, 17) : false
filled_19 = display_count > 18 and array.size(grid_filled) > 18 ? array.get(grid_filled, 18) : false
filled_20 = display_count > 19 and array.size(grid_filled) > 19 ? array.get(grid_filled, 19) : false
filled_21 = display_count > 20 and array.size(grid_filled) > 20 ? array.get(grid_filled, 20) : false
filled_22 = display_count > 21 and array.size(grid_filled) > 21 ? array.get(grid_filled, 21) : false
filled_23 = display_count > 22 and array.size(grid_filled) > 22 ? array.get(grid_filled, 22) : false
filled_24 = display_count > 23 and array.size(grid_filled) > 23 ? array.get(grid_filled, 23) : false
filled_25 = display_count > 24 and array.size(grid_filled) > 24 ? array.get(grid_filled, 24) : false

// 在全局作用域中绘制所有25个网格线
plot(grid_1, "网格1", color=filled_1 ? color.red : color.blue, linewidth=1)
plot(grid_2, "网格2", color=filled_2 ? color.red : color.blue, linewidth=1)
plot(grid_3, "网格3", color=filled_3 ? color.red : color.blue, linewidth=1)
plot(grid_4, "网格4", color=filled_4 ? color.red : color.blue, linewidth=1)
plot(grid_5, "网格5", color=filled_5 ? color.red : color.blue, linewidth=1)
plot(grid_6, "网格6", color=filled_6 ? color.red : color.blue, linewidth=1)
plot(grid_7, "网格7", color=filled_7 ? color.red : color.blue, linewidth=1)
plot(grid_8, "网格8", color=filled_8 ? color.red : color.blue, linewidth=1)
plot(grid_9, "网格9", color=filled_9 ? color.red : color.blue, linewidth=1)
plot(grid_10, "网格10", color=filled_10 ? color.red : color.blue, linewidth=1)
plot(grid_11, "网格11", color=filled_11 ? color.red : color.blue, linewidth=1)
plot(grid_12, "网格12", color=filled_12 ? color.red : color.blue, linewidth=1)
plot(grid_13, "网格13", color=filled_13 ? color.red : color.blue, linewidth=1)
plot(grid_14, "网格14", color=filled_14 ? color.red : color.blue, linewidth=1)
plot(grid_15, "网格15", color=filled_15 ? color.red : color.blue, linewidth=1)
plot(grid_16, "网格16", color=filled_16 ? color.red : color.blue, linewidth=1)
plot(grid_17, "网格17", color=filled_17 ? color.red : color.blue, linewidth=1)
plot(grid_18, "网格18", color=filled_18 ? color.red : color.blue, linewidth=1)
plot(grid_19, "网格19", color=filled_19 ? color.red : color.blue, linewidth=1)
plot(grid_20, "网格20", color=filled_20 ? color.red : color.blue, linewidth=1)
plot(grid_21, "网格21", color=filled_21 ? color.red : color.blue, linewidth=1)
plot(grid_22, "网格22", color=filled_22 ? color.red : color.blue, linewidth=1)
plot(grid_23, "网格23", color=filled_23 ? color.red : color.blue, linewidth=1)
plot(grid_24, "网格24", color=filled_24 ? color.red : color.blue, linewidth=1)
plot(grid_25, "网格25", color=filled_25 ? color.red : color.blue, linewidth=1)

// 显示策略信息
var table info_table = table.new(position.top_right, 2, 18, bgcolor=color.white, border_width=1)
if barstate.islast
    table.cell(info_table, 0, 0, "回测状态", text_color=color.black)
    table.cell(info_table, 1, 0, in_date_range ? "回测中" : "回测外", text_color=in_date_range ? color.green : color.gray)

    table.cell(info_table, 0, 1, "策略状态", text_color=color.black)
    table.cell(info_table, 1, 1, strategy_active ? "运行中" : "未激活", text_color=strategy_active ? color.green : color.red)

    table.cell(info_table, 0, 2, "网格模式", text_color=color.black)
    grid_mode_text = rebound_confirmation_enabled ? "回调确认" : "直接触发"
    grid_mode_color = rebound_confirmation_enabled ? color.purple : color.orange
    table.cell(info_table, 1, 2, grid_mode_text, text_color=grid_mode_color)

    table.cell(info_table, 0, 3, "网格层数", text_color=color.black)
    table.cell(info_table, 1, 3, str.tostring(grid_levels), text_color=color.black)

    table.cell(info_table, 0, 4, "显示网格数", text_color=color.black)
    table.cell(info_table, 1, 4, show_grid_lines ? str.tostring(grid_display_count) : "隐藏", text_color=show_grid_lines ? color.blue : color.gray)

    table.cell(info_table, 0, 5, "当前持仓", text_color=color.black)
    table.cell(info_table, 1, 5, str.tostring(strategy.position_size, "#.####"), text_color=color.black)

    table.cell(info_table, 0, 6, "平均成本", text_color=color.black)
    table.cell(info_table, 1, 6, str.tostring(avg_entry_price, "#.####"), text_color=color.black)

    table.cell(info_table, 0, 7, "当前盈亏%", text_color=color.black)
    current_pnl = strategy.position_size > 0 and avg_entry_price > 0 ? (close - avg_entry_price) / avg_entry_price * 100 : 0
    pnl_color = current_pnl >= 0 ? color.green : color.red
    table.cell(info_table, 1, 7, str.tostring(current_pnl, "#.##") + "%", text_color=pnl_color)

    table.cell(info_table, 0, 8, "已建仓网格", text_color=color.black)
    filled_count = 0
    if array.size(grid_filled) > 0
        for i = 0 to array.size(grid_filled) - 1
            if array.get(grid_filled, i)
                filled_count += 1
    table.cell(info_table, 1, 8, str.tostring(filled_count), text_color=color.black)

    table.cell(info_table, 0, 9, "等待回调确认", text_color=color.black)
    waiting_count = 0
    if rebound_confirmation_enabled and array.size(grid_waiting_rebound) > 0
        for i = 0 to array.size(grid_waiting_rebound) - 1
            if array.get(grid_waiting_rebound, i)
                waiting_count += 1
    waiting_color = waiting_count > 0 ? color.orange : color.gray
    table.cell(info_table, 1, 9, str.tostring(waiting_count), text_color=waiting_color)

    table.cell(info_table, 0, 10, "回调幅度范围", text_color=color.black)
    // 显示当前等待回调的网格及其所需回调幅度
    rebound_range_text = ""
    rebound_range_color = color.gray

    if rebound_confirmation_enabled
        current_waiting_info = ""
        if array.size(grid_waiting_rebound) > 0
            for i = 0 to array.size(grid_waiting_rebound) - 1
                if array.get(grid_waiting_rebound, i)
                    layer_num = i + 1
                    required_pct = get_rebound_pct(layer_num)
                    current_waiting_info := current_waiting_info + str.tostring(layer_num) + "层:" + str.tostring(required_pct, "#.#") + "% "
        rebound_range_text := current_waiting_info != "" ? current_waiting_info : "0.2%-0.8%"
        rebound_range_color := current_waiting_info != "" ? color.orange : color.purple
    else
        rebound_range_text := "未启用"
        rebound_range_color := color.gray

    table.cell(info_table, 1, 10, rebound_range_text, text_color=rebound_range_color)

    table.cell(info_table, 0, 11, "本K线建仓", text_color=color.black)
    entry_status_text = entry_executed_this_bar ? "已执行" : "未执行"
    entry_status_color = entry_executed_this_bar ? color.green : color.gray
    table.cell(info_table, 1, 11, entry_status_text, text_color=entry_status_color)

    table.cell(info_table, 0, 12, "等待下跌", text_color=color.black)
    decline_status_text = waiting_for_decline ? "是" : "否"
    decline_status_color = waiting_for_decline ? color.orange : color.green
    decline_info = waiting_for_decline and last_entry_price > 0 ? decline_status_text + "(" + str.tostring(last_entry_price, "#.####") + ")" : decline_status_text
    table.cell(info_table, 1, 12, decline_info, text_color=decline_status_color)

    table.cell(info_table, 0, 13, "最高盈利%", text_color=color.black)
    table.cell(info_table, 1, 13, str.tostring(highest_profit, "#.##") + "%", text_color=color.green)

    table.cell(info_table, 0, 14, "马丁倍数", text_color=color.black)
    martin_text = martingale_enabled ? str.tostring(martingale_multiplier, "#.#") + "倍" : "未启用"
    martin_color = martingale_enabled ? color.red : color.gray
    table.cell(info_table, 1, 14, martin_text, text_color=martin_color)

    table.cell(info_table, 0, 15, "金字塔层数", text_color=color.black)
    table.cell(info_table, 1, 15, "999", text_color=color.blue)

    table.cell(info_table, 0, 16, "追踪止盈", text_color=color.black)
    table.cell(info_table, 1, 16, "1%/2%/3%/4%", text_color=color.purple)

    table.cell(info_table, 0, 17, "手续费率", text_color=color.black)
    table.cell(info_table, 1, 17, "0.075%", text_color=color.orange)
