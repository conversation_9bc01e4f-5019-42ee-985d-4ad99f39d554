//@version=5
strategy("保守策略", overlay=true, initial_capital=1000, default_qty_type=strategy.percent_of_equity, default_qty_value=1, pyramiding=999, commission_type=strategy.commission.percent, commission_value=0.075)

// ==================== 策略参数设置 ====================
// 基础设置说明
// 金字塔功能：999层（允许多次加仓）
// 手续费设置：0.075%（双向手续费）

// 网格参数
grid_levels = input.int(25, "网格层数", minval=3, maxval=50)

// 25层独立网格间距设置
grid_spacing_1 = input.float(1.2, "第1层间距(%)", minval=0.1, maxval=50.0, step=0.1, group="网格间距设置")
grid_spacing_2 = input.float(1.2, "第2层间距(%)", minval=0.1, maxval=50.0, step=0.1, group="网格间距设置")
grid_spacing_3 = input.float(1.2, "第3层间距(%)", minval=0.1, maxval=50.0, step=0.1, group="网格间距设置")
grid_spacing_4 = input.float(1.2, "第4层间距(%)", minval=0.1, maxval=50.0, step=0.1, group="网格间距设置")
grid_spacing_5 = input.float(2.4, "第5层间距(%)", minval=0.1, maxval=50.0, step=0.1, group="网格间距设置")
grid_spacing_6 = input.float(2.4, "第6层间距(%)", minval=0.1, maxval=50.0, step=0.1, group="网格间距设置")
grid_spacing_7 = input.float(2.4, "第7层间距(%)", minval=0.1, maxval=50.0, step=0.1, group="网格间距设置")
grid_spacing_8 = input.float(2.4, "第8层间距(%)", minval=0.1, maxval=50.0, step=0.1, group="网格间距设置")
grid_spacing_9 = input.float(3.3, "第9层间距(%)", minval=0.1, maxval=50.0, step=0.1, group="网格间距设置")
grid_spacing_10 = input.float(3.3, "第10层间距(%)", minval=0.1, maxval=50.0, step=0.1, group="网格间距设置")
grid_spacing_11 = input.float(3.3, "第11层间距(%)", minval=0.1, maxval=50.0, step=0.1, group="网格间距设置")
grid_spacing_12 = input.float(3.3, "第12层间距(%)", minval=0.1, maxval=50.0, step=0.1, group="网格间距设置")
grid_spacing_13 = input.float(3.3, "第13层间距(%)", minval=0.1, maxval=50.0, step=0.1, group="网格间距设置")
grid_spacing_14 = input.float(3.3, "第14层间距(%)", minval=0.1, maxval=50.0, step=0.1, group="网格间距设置")
grid_spacing_15 = input.float(3.3, "第15层间距(%)", minval=0.1, maxval=50.0, step=0.1, group="网格间距设置")
grid_spacing_16 = input.float(3.3, "第16层间距(%)", minval=0.1, maxval=50.0, step=0.1, group="网格间距设置")
grid_spacing_17 = input.float(3.3, "第17层间距(%)", minval=0.1, maxval=50.0, step=0.1, group="网格间距设置")
grid_spacing_18 = input.float(3.3, "第18层间距(%)", minval=0.1, maxval=50.0, step=0.1, group="网格间距设置")
grid_spacing_19 = input.float(3.3, "第19层间距(%)", minval=0.1, maxval=50.0, step=0.1, group="网格间距设置")
grid_spacing_20 = input.float(3.3, "第20层间距(%)", minval=0.1, maxval=50.0, step=0.1, group="网格间距设置")
grid_spacing_21 = input.float(3.3, "第21层间距(%)", minval=0.1, maxval=50.0, step=0.1, group="网格间距设置")
grid_spacing_22 = input.float(3.3, "第22层间距(%)", minval=0.1, maxval=50.0, step=0.1, group="网格间距设置")
grid_spacing_23 = input.float(3.3, "第23层间距(%)", minval=0.1, maxval=50.0, step=0.1, group="网格间距设置")
grid_spacing_24 = input.float(3.3, "第24层间距(%)", minval=0.1, maxval=50.0, step=0.1, group="网格间距设置")
grid_spacing_25 = input.float(3.3, "第25层间距(%)", minval=0.1, maxval=50.0, step=0.1, group="网格间距设置")

// 单网格追踪止盈参数
grid_trailing_1_target = input.float(1.0, "追踪止盈1目标(%)", minval=0.1, maxval=20.0, step=0.1, group="单网格追踪止盈")
grid_trailing_1_callback = input.float(0.2, "追踪止盈1回调(%)", minval=0.1, maxval=5.0, step=0.1, group="单网格追踪止盈")
grid_trailing_2_target = input.float(2.0, "追踪止盈2目标(%)", minval=0.1, maxval=20.0, step=0.1, group="单网格追踪止盈")
grid_trailing_2_callback = input.float(0.3, "追踪止盈2回调(%)", minval=0.1, maxval=5.0, step=0.1, group="单网格追踪止盈")
grid_trailing_3_target = input.float(3.0, "追踪止盈3目标(%)", minval=0.1, maxval=20.0, step=0.1, group="单网格追踪止盈")
grid_trailing_3_callback = input.float(0.4, "追踪止盈3回调(%)", minval=0.1, maxval=5.0, step=0.1, group="单网格追踪止盈")
grid_trailing_4_target = input.float(4.0, "追踪止盈4目标(%)", minval=0.1, maxval=20.0, step=0.1, group="单网格追踪止盈")
grid_trailing_4_callback = input.float(0.5, "追踪止盈4回调(%)", minval=0.1, maxval=5.0, step=0.1, group="单网格追踪止盈")

// 回调确认参数
rebound_confirmation_enabled = input.bool(true, "启用回调确认建仓", group="回调确认设置")
rebound_confirmation_pct = input.float(0.2, "回调确认幅度(%)", minval=0.1, maxval=2.0, step=0.1, group="回调确认设置")

// 马丁参数
martingale_enabled = input.bool(true, "启用马丁策略")
martingale_multiplier = input.float(2.0, "马丁倍数", minval=1, maxval=3.0, step=0.1)

// 止盈止损参数
overall_profit_target = input.float(3.0, "整体止盈(%)", minval=0.1, maxval=20.0, step=0.5)
trailing_stop_enabled = input.bool(true, "启用追踪止盈")
trailing_stop_pct = input.float(0.5, "追踪止盈回撤(%)", minval=0.1, maxval=8.0, step=0.5)

// 回测时间设置
start_date = input.time(timestamp("2025-06-01 00:00"), "回测开始时间")
end_date = input.time(timestamp("2025-12-31 23:59"), "回测结束时间")

// 图表显示参数
show_grid_lines = input.bool(true, "显示网格线")
grid_display_count = input.int(4, "显示网格数量", minval=1, maxval=25)

// ==================== 全局变量 ====================
var array<float> grid_prices = array.new<float>()
var array<bool> grid_filled = array.new<bool>()
var array<string> grid_ids = array.new<string>()
var array<float> grid_entry_prices = array.new<float>()
var array<float> grid_highest_profits = array.new<float>()

// 回调确认相关数组
var array<bool> grid_triggered = array.new<bool>()      // 记录网格是否已触发（价格跌破网格线）
var array<float> grid_min_prices = array.new<float>()   // 记录触发后的最低价
var array<bool> grid_waiting_rebound = array.new<bool>() // 记录是否正在等待回调确认

var float base_price = na
var float total_position_size = 0.0
var float avg_entry_price = 0.0
var int martingale_count = 0
var float highest_profit = 0.0
var bool strategy_active = false

// ==================== 辅助函数 ====================
// 获取指定层级的网格间距
get_grid_spacing(layer) =>
    switch layer
        1 => grid_spacing_1
        2 => grid_spacing_2
        3 => grid_spacing_3
        4 => grid_spacing_4
        5 => grid_spacing_5
        6 => grid_spacing_6
        7 => grid_spacing_7
        8 => grid_spacing_8
        9 => grid_spacing_9
        10 => grid_spacing_10
        11 => grid_spacing_11
        12 => grid_spacing_12
        13 => grid_spacing_13
        14 => grid_spacing_14
        15 => grid_spacing_15
        16 => grid_spacing_16
        17 => grid_spacing_17
        18 => grid_spacing_18
        19 => grid_spacing_19
        20 => grid_spacing_20
        21 => grid_spacing_21
        22 => grid_spacing_22
        23 => grid_spacing_23
        24 => grid_spacing_24
        25 => grid_spacing_25
        => 2.0  // 默认值

// ==================== 主策略逻辑 ====================
// 时间过滤
in_date_range = time >= start_date and time <= end_date

// 初始化网格（只在回测时间范围内且第一次运行时）
if in_date_range and na(base_price)
    base_price := close
    strategy_active := true

    // 清空并初始化数组
    array.clear(grid_prices)
    array.clear(grid_filled)
    array.clear(grid_ids)
    array.clear(grid_entry_prices)
    array.clear(grid_highest_profits)
    array.clear(grid_triggered)
    array.clear(grid_min_prices)
    array.clear(grid_waiting_rebound)

    // 首单均价建仓
    first_position_size = strategy.initial_capital * 0.1 / close
    strategy.entry("首单", strategy.long, qty=first_position_size)
    total_position_size := first_position_size
    avg_entry_price := close

    // 创建25个网格层（使用累积间距计算）
    cumulative_spacing = 0.0
    for i = 1 to grid_levels
        layer_spacing = get_grid_spacing(i)
        cumulative_spacing := cumulative_spacing + layer_spacing
        grid_price = base_price * (1 - cumulative_spacing / 100)
        array.push(grid_prices, grid_price)
        array.push(grid_filled, false)
        array.push(grid_ids, "建仓-" + str.tostring(i))
        array.push(grid_entry_prices, 0.0)
        array.push(grid_highest_profits, 0.0)
        array.push(grid_triggered, false)
        array.push(grid_min_prices, 0.0)
        array.push(grid_waiting_rebound, false)

// 检查网格触发（只在回测时间范围内）
if in_date_range and strategy_active and array.size(grid_prices) > 0
    for i = 0 to array.size(grid_prices) - 1
        grid_price = array.get(grid_prices, i)
        is_filled = array.get(grid_filled, i)
        grid_id = array.get(grid_ids, i)
        is_triggered = array.get(grid_triggered, i)
        min_price = array.get(grid_min_prices, i)
        waiting_rebound = array.get(grid_waiting_rebound, i)

        // 如果网格未建仓
        if not is_filled
            if rebound_confirmation_enabled
                // 回调确认模式
                if close <= grid_price and not is_triggered
                    // 首次触发网格线，开始追踪最低价
                    array.set(grid_triggered, i, true)
                    array.set(grid_min_prices, i, close)
                    array.set(grid_waiting_rebound, i, true)

                if is_triggered and waiting_rebound
                    // 更新最低价
                    if close < min_price
                        array.set(grid_min_prices, i, close)

                    // 检查回调确认条件
                    rebound_pct = (close - min_price) / min_price * 100
                    if rebound_pct >= rebound_confirmation_pct
                        // 满足回调确认条件，执行建仓
                        position_size = strategy.initial_capital * 0.1 / close

                        // 应用马丁倍数（从第二层开始）
                        if martingale_enabled and i > 0
                            // 修改马丁逻辑，每3层使用相同倍率，每3层翻倍
                            layer_index = i + 1  // 转换为1-based索引，方便计算
                            // 计算当前层所在的3层组（1-3为第1组，4-6为第2组，以此类推）
                            layer_group = math.ceil(layer_index / 3)
                            // 计算当前组的马丁倍率
                            // 第1组为1倍，第2组为2倍，第3组为4倍，第4组为8倍...
                            multiplier = layer_group > 1 ? math.pow(2, layer_group - 1) : 1
                            position_size := position_size * multiplier

                        strategy.entry(grid_id, strategy.long, qty=position_size)
                        array.set(grid_filled, i, true)
                        array.set(grid_entry_prices, i, close)
                        array.set(grid_highest_profits, i, 0.0)
                        // 重置回调确认状态
                        array.set(grid_triggered, i, false)
                        array.set(grid_min_prices, i, 0.0)
                        array.set(grid_waiting_rebound, i, false)

                        // 更新平均成本计算（考虑首单建仓）
                        old_total_cost = avg_entry_price * total_position_size
                        new_cost = close * position_size
                        total_position_size := total_position_size + position_size
                        avg_entry_price := (old_total_cost + new_cost) / total_position_size
            else
                // 原始模式：价格触及网格线即建仓
                if close <= grid_price
                    position_size = strategy.initial_capital * 0.1 / close

                    // 应用马丁倍数（从第二层开始）
                    if martingale_enabled and i > 0
                        // 修改马丁逻辑，每3层使用相同倍率，每3层翻倍
                        layer_index = i + 1  // 转换为1-based索引，方便计算
                        // 计算当前层所在的3层组（1-3为第1组，4-6为第2组，以此类推）
                        layer_group = math.ceil(layer_index / 3)
                        // 计算当前组的马丁倍率
                        // 第1组为1倍，第2组为2倍，第3组为4倍，第4组为8倍...
                        multiplier = layer_group > 1 ? math.pow(2, layer_group - 1) : 1
                        position_size := position_size * multiplier

                    strategy.entry(grid_id, strategy.long, qty=position_size)
                    array.set(grid_filled, i, true)
                    array.set(grid_entry_prices, i, close)
                    array.set(grid_highest_profits, i, 0.0)
                    // 更新平均成本计算（考虑首单建仓）
                    old_total_cost = avg_entry_price * total_position_size
                    new_cost = close * position_size
                    total_position_size := total_position_size + position_size
                    avg_entry_price := (old_total_cost + new_cost) / total_position_size

// 检查止盈条件（只在回测时间范围内）
if in_date_range and strategy.position_size > 0 and array.size(grid_prices) > 0
    current_profit_pct = (close - avg_entry_price) / avg_entry_price * 100

    // 更新最高盈利
    if current_profit_pct > highest_profit
        highest_profit := current_profit_pct

    // 单网格追踪止盈检查
    for i = 0 to array.size(grid_prices) - 1
        grid_price = array.get(grid_prices, i)
        is_filled = array.get(grid_filled, i)
        grid_id = array.get(grid_ids, i)

        if is_filled
            entry_price = array.get(grid_entry_prices, i)
            current_profit_pct = (close - entry_price) / entry_price * 100
            highest_profit_pct = array.get(grid_highest_profits, i)

            // 更新最高盈利
            if current_profit_pct > highest_profit_pct
                array.set(grid_highest_profits, i, current_profit_pct)
                highest_profit_pct := current_profit_pct

            // 检查追踪止盈条件
            should_close = false
            close_reason = ""

            // 追踪止盈1: 1%目标，0.2%回调
            if highest_profit_pct >= grid_trailing_1_target and current_profit_pct <= highest_profit_pct - grid_trailing_1_callback
                should_close := true
                close_reason := "追踪止盈1"

            // 追踪止盈2: 2%目标，0.3%回调
            if highest_profit_pct >= grid_trailing_2_target and current_profit_pct <= highest_profit_pct - grid_trailing_2_callback
                should_close := true
                close_reason := "追踪止盈2"

            // 追踪止盈3: 3%目标，0.4%回调
            if highest_profit_pct >= grid_trailing_3_target and current_profit_pct <= highest_profit_pct - grid_trailing_3_callback
                should_close := true
                close_reason := "追踪止盈3"

            // 追踪止盈4: 4%目标，0.5%回调
            if highest_profit_pct >= grid_trailing_4_target and current_profit_pct <= highest_profit_pct - grid_trailing_4_callback
                should_close := true
                close_reason := "追踪止盈4"

            // 执行平仓
            if should_close
                strategy.close(grid_id, comment=close_reason)
                array.set(grid_filled, i, false)
                array.set(grid_entry_prices, i, 0.0)
                array.set(grid_highest_profits, i, 0.0)
                // 重置回调确认状态
                array.set(grid_triggered, i, false)
                array.set(grid_min_prices, i, 0.0)
                array.set(grid_waiting_rebound, i, false)

    // 整体止盈检查
    if current_profit_pct >= overall_profit_target
        if trailing_stop_enabled
            // 追踪止盈
            if current_profit_pct < highest_profit - trailing_stop_pct
                strategy.close_all("追踪止盈")
                // 重置策略状态
                base_price := na
                total_position_size := 0.0
                avg_entry_price := 0.0
                martingale_count := 0
                highest_profit := 0.0
                strategy_active := false
                array.clear(grid_filled)
                array.clear(grid_entry_prices)
                array.clear(grid_highest_profits)
                array.clear(grid_triggered)
                array.clear(grid_min_prices)
                array.clear(grid_waiting_rebound)
        else
            strategy.close_all("整体止盈")
            // 重置策略状态
            base_price := na
            total_position_size := 0.0
            avg_entry_price := 0.0
            martingale_count := 0
            highest_profit := 0.0
            strategy_active := false
            array.clear(grid_filled)
            array.clear(grid_entry_prices)
            array.clear(grid_highest_profits)
            array.clear(grid_triggered)
            array.clear(grid_min_prices)
            array.clear(grid_waiting_rebound)

// ==================== 图表显示 ====================
// 绘制基准价格线
plot(base_price, "基准价格", color=color.yellow, linewidth=2)

// 准备网格线数据（在全局作用域中）
display_count = show_grid_lines and array.size(grid_prices) > 0 ? math.min(grid_display_count, array.size(grid_prices)) : 0

// 绘制所有25个网格线
grid_1 = display_count > 0 and array.size(grid_prices) > 0 ? array.get(grid_prices, 0) : na
grid_2 = display_count > 1 and array.size(grid_prices) > 1 ? array.get(grid_prices, 1) : na
grid_3 = display_count > 2 and array.size(grid_prices) > 2 ? array.get(grid_prices, 2) : na
grid_4 = display_count > 3 and array.size(grid_prices) > 3 ? array.get(grid_prices, 3) : na
grid_5 = display_count > 4 and array.size(grid_prices) > 4 ? array.get(grid_prices, 4) : na
grid_6 = display_count > 5 and array.size(grid_prices) > 5 ? array.get(grid_prices, 5) : na
grid_7 = display_count > 6 and array.size(grid_prices) > 6 ? array.get(grid_prices, 6) : na
grid_8 = display_count > 7 and array.size(grid_prices) > 7 ? array.get(grid_prices, 7) : na
grid_9 = display_count > 8 and array.size(grid_prices) > 8 ? array.get(grid_prices, 8) : na
grid_10 = display_count > 9 and array.size(grid_prices) > 9 ? array.get(grid_prices, 9) : na
grid_11 = display_count > 10 and array.size(grid_prices) > 10 ? array.get(grid_prices, 10) : na
grid_12 = display_count > 11 and array.size(grid_prices) > 11 ? array.get(grid_prices, 11) : na
grid_13 = display_count > 12 and array.size(grid_prices) > 12 ? array.get(grid_prices, 12) : na
grid_14 = display_count > 13 and array.size(grid_prices) > 13 ? array.get(grid_prices, 13) : na
grid_15 = display_count > 14 and array.size(grid_prices) > 14 ? array.get(grid_prices, 14) : na
grid_16 = display_count > 15 and array.size(grid_prices) > 15 ? array.get(grid_prices, 15) : na
grid_17 = display_count > 16 and array.size(grid_prices) > 16 ? array.get(grid_prices, 16) : na
grid_18 = display_count > 17 and array.size(grid_prices) > 17 ? array.get(grid_prices, 17) : na
grid_19 = display_count > 18 and array.size(grid_prices) > 18 ? array.get(grid_prices, 18) : na
grid_20 = display_count > 19 and array.size(grid_prices) > 19 ? array.get(grid_prices, 19) : na
grid_21 = display_count > 20 and array.size(grid_prices) > 20 ? array.get(grid_prices, 20) : na
grid_22 = display_count > 21 and array.size(grid_prices) > 21 ? array.get(grid_prices, 21) : na
grid_23 = display_count > 22 and array.size(grid_prices) > 22 ? array.get(grid_prices, 22) : na
grid_24 = display_count > 23 and array.size(grid_prices) > 23 ? array.get(grid_prices, 23) : na
grid_25 = display_count > 24 and array.size(grid_prices) > 24 ? array.get(grid_prices, 24) : na

filled_1 = display_count > 0 and array.size(grid_filled) > 0 ? array.get(grid_filled, 0) : false
filled_2 = display_count > 1 and array.size(grid_filled) > 1 ? array.get(grid_filled, 1) : false
filled_3 = display_count > 2 and array.size(grid_filled) > 2 ? array.get(grid_filled, 2) : false
filled_4 = display_count > 3 and array.size(grid_filled) > 3 ? array.get(grid_filled, 3) : false
filled_5 = display_count > 4 and array.size(grid_filled) > 4 ? array.get(grid_filled, 4) : false
filled_6 = display_count > 5 and array.size(grid_filled) > 5 ? array.get(grid_filled, 5) : false
filled_7 = display_count > 6 and array.size(grid_filled) > 6 ? array.get(grid_filled, 6) : false
filled_8 = display_count > 7 and array.size(grid_filled) > 7 ? array.get(grid_filled, 7) : false
filled_9 = display_count > 8 and array.size(grid_filled) > 8 ? array.get(grid_filled, 8) : false
filled_10 = display_count > 9 and array.size(grid_filled) > 9 ? array.get(grid_filled, 9) : false
filled_11 = display_count > 10 and array.size(grid_filled) > 10 ? array.get(grid_filled, 10) : false
filled_12 = display_count > 11 and array.size(grid_filled) > 11 ? array.get(grid_filled, 11) : false
filled_13 = display_count > 12 and array.size(grid_filled) > 12 ? array.get(grid_filled, 12) : false
filled_14 = display_count > 13 and array.size(grid_filled) > 13 ? array.get(grid_filled, 13) : false
filled_15 = display_count > 14 and array.size(grid_filled) > 14 ? array.get(grid_filled, 14) : false
filled_16 = display_count > 15 and array.size(grid_filled) > 15 ? array.get(grid_filled, 15) : false
filled_17 = display_count > 16 and array.size(grid_filled) > 16 ? array.get(grid_filled, 16) : false
filled_18 = display_count > 17 and array.size(grid_filled) > 17 ? array.get(grid_filled, 17) : false
filled_19 = display_count > 18 and array.size(grid_filled) > 18 ? array.get(grid_filled, 18) : false
filled_20 = display_count > 19 and array.size(grid_filled) > 19 ? array.get(grid_filled, 19) : false
filled_21 = display_count > 20 and array.size(grid_filled) > 20 ? array.get(grid_filled, 20) : false
filled_22 = display_count > 21 and array.size(grid_filled) > 21 ? array.get(grid_filled, 21) : false
filled_23 = display_count > 22 and array.size(grid_filled) > 22 ? array.get(grid_filled, 22) : false
filled_24 = display_count > 23 and array.size(grid_filled) > 23 ? array.get(grid_filled, 23) : false
filled_25 = display_count > 24 and array.size(grid_filled) > 24 ? array.get(grid_filled, 24) : false

// 在全局作用域中绘制所有25个网格线
plot(grid_1, "网格1", color=filled_1 ? color.red : color.blue, linewidth=1)
plot(grid_2, "网格2", color=filled_2 ? color.red : color.blue, linewidth=1)
plot(grid_3, "网格3", color=filled_3 ? color.red : color.blue, linewidth=1)
plot(grid_4, "网格4", color=filled_4 ? color.red : color.blue, linewidth=1)
plot(grid_5, "网格5", color=filled_5 ? color.red : color.blue, linewidth=1)
plot(grid_6, "网格6", color=filled_6 ? color.red : color.blue, linewidth=1)
plot(grid_7, "网格7", color=filled_7 ? color.red : color.blue, linewidth=1)
plot(grid_8, "网格8", color=filled_8 ? color.red : color.blue, linewidth=1)
plot(grid_9, "网格9", color=filled_9 ? color.red : color.blue, linewidth=1)
plot(grid_10, "网格10", color=filled_10 ? color.red : color.blue, linewidth=1)
plot(grid_11, "网格11", color=filled_11 ? color.red : color.blue, linewidth=1)
plot(grid_12, "网格12", color=filled_12 ? color.red : color.blue, linewidth=1)
plot(grid_13, "网格13", color=filled_13 ? color.red : color.blue, linewidth=1)
plot(grid_14, "网格14", color=filled_14 ? color.red : color.blue, linewidth=1)
plot(grid_15, "网格15", color=filled_15 ? color.red : color.blue, linewidth=1)
plot(grid_16, "网格16", color=filled_16 ? color.red : color.blue, linewidth=1)
plot(grid_17, "网格17", color=filled_17 ? color.red : color.blue, linewidth=1)
plot(grid_18, "网格18", color=filled_18 ? color.red : color.blue, linewidth=1)
plot(grid_19, "网格19", color=filled_19 ? color.red : color.blue, linewidth=1)
plot(grid_20, "网格20", color=filled_20 ? color.red : color.blue, linewidth=1)
plot(grid_21, "网格21", color=filled_21 ? color.red : color.blue, linewidth=1)
plot(grid_22, "网格22", color=filled_22 ? color.red : color.blue, linewidth=1)
plot(grid_23, "网格23", color=filled_23 ? color.red : color.blue, linewidth=1)
plot(grid_24, "网格24", color=filled_24 ? color.red : color.blue, linewidth=1)
plot(grid_25, "网格25", color=filled_25 ? color.red : color.blue, linewidth=1)

// 显示策略信息
var table info_table = table.new(position.top_right, 2, 14, bgcolor=color.white, border_width=1)
if barstate.islast
    table.cell(info_table, 0, 0, "回测状态", text_color=color.black)
    table.cell(info_table, 1, 0, in_date_range ? "回测中" : "回测外", text_color=in_date_range ? color.green : color.gray)

    table.cell(info_table, 0, 1, "策略状态", text_color=color.black)
    table.cell(info_table, 1, 1, strategy_active ? "运行中" : "未激活", text_color=strategy_active ? color.green : color.red)

    table.cell(info_table, 0, 2, "网格模式", text_color=color.black)
    grid_mode_text = rebound_confirmation_enabled ? "回调确认" : "直接触发"
    grid_mode_color = rebound_confirmation_enabled ? color.purple : color.orange
    table.cell(info_table, 1, 2, grid_mode_text, text_color=grid_mode_color)

    table.cell(info_table, 0, 3, "网格层数", text_color=color.black)
    table.cell(info_table, 1, 3, str.tostring(grid_levels), text_color=color.black)

    table.cell(info_table, 0, 4, "显示网格数", text_color=color.black)
    table.cell(info_table, 1, 4, show_grid_lines ? str.tostring(grid_display_count) : "隐藏", text_color=show_grid_lines ? color.blue : color.gray)

    table.cell(info_table, 0, 5, "当前持仓", text_color=color.black)
    table.cell(info_table, 1, 5, str.tostring(strategy.position_size, "#.####"), text_color=color.black)

    table.cell(info_table, 0, 6, "平均成本", text_color=color.black)
    table.cell(info_table, 1, 6, str.tostring(avg_entry_price, "#.####"), text_color=color.black)

    table.cell(info_table, 0, 7, "当前盈亏%", text_color=color.black)
    current_pnl = strategy.position_size > 0 and avg_entry_price > 0 ? (close - avg_entry_price) / avg_entry_price * 100 : 0
    pnl_color = current_pnl >= 0 ? color.green : color.red
    table.cell(info_table, 1, 7, str.tostring(current_pnl, "#.##") + "%", text_color=pnl_color)

    table.cell(info_table, 0, 8, "已建仓网格", text_color=color.black)
    filled_count = 0
    if array.size(grid_filled) > 0
        for i = 0 to array.size(grid_filled) - 1
            if array.get(grid_filled, i)
                filled_count += 1
    table.cell(info_table, 1, 8, str.tostring(filled_count), text_color=color.black)

    table.cell(info_table, 0, 9, "等待回调确认", text_color=color.black)
    waiting_count = 0
    if rebound_confirmation_enabled and array.size(grid_waiting_rebound) > 0
        for i = 0 to array.size(grid_waiting_rebound) - 1
            if array.get(grid_waiting_rebound, i)
                waiting_count += 1
    waiting_color = waiting_count > 0 ? color.orange : color.gray
    table.cell(info_table, 1, 9, str.tostring(waiting_count), text_color=waiting_color)

    table.cell(info_table, 0, 10, "最高盈利%", text_color=color.black)
    table.cell(info_table, 1, 10, str.tostring(highest_profit, "#.##") + "%", text_color=color.green)

    table.cell(info_table, 0, 11, "金字塔层数", text_color=color.black)
    table.cell(info_table, 1, 11, "999", text_color=color.blue)

    table.cell(info_table, 0, 12, "追踪止盈", text_color=color.black)
    table.cell(info_table, 1, 12, "1%/2%/3%/4%", text_color=color.purple)

    table.cell(info_table, 0, 13, "手续费率", text_color=color.black)
    table.cell(info_table, 1, 13, "0.075%", text_color=color.orange)
